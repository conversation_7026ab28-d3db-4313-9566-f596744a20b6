package repo

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gorm.io/gorm/clause"
)

type CheckinOption func(repository.IRepository[models.Checkin])

var Checkin = func(c core.IContext, options ...CheckinOption) repository.IRepository[models.Checkin] {
	r := repository.New[models.Checkin](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func CheckinOrderBy(pageOptions *core.PageOptions) CheckinOption {
	return func(c repository.IRepository[models.Checkin]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func CheckinWithUser(id *string) CheckinOption {
	return func(c repository.IRepository[models.Checkin]) {
		if id == nil {
			return
		}
		c.Where("user_id = ?", id)
	}
}

func CheckinWithDateRange(startDate *string, endDate *string) CheckinOption {
	return func(c repository.IRepository[models.Checkin]) {
		if startDate != nil && endDate != nil {
			c.Where("date BETWEEN ? AND ?", startDate, endDate)
		} else if startDate != nil {
			c.Where("date >= ?", startDate)
		} else if endDate != nil {
			c.Where("date <= ?", endDate)
		}
	}
}

func CheckinWithTeamCode(teamCode *string) CheckinOption {
	return func(c repository.IRepository[models.Checkin]) {
		if teamCode == nil {
			return
		}
		c.Joins("JOIN users ON users.id = checkins.user_id").Where("users.team_code = ?", teamCode)
	}
}

func CheckinWithType(checkinType *string) CheckinOption {
	return func(c repository.IRepository[models.Checkin]) {
		if checkinType == nil {
			return
		}
		c.Where("type = ?", checkinType)
	}
}

func CheckinWithAllRelation() CheckinOption {
	return func(c repository.IRepository[models.Checkin]) {
		c.Preload(clause.Associations).Preload("User.Team")
	}
}
