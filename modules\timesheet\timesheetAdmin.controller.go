package timesheet

import (
	"net/http"

	"gitlab.finema.co/finema/finework/finework-api/requests"
	"gitlab.finema.co/finema/finework/finework-api/services"
	core "gitlab.finema.co/finema/idin-core"
)

type TimesheetAdminController struct {
}

func (m TimesheetAdminController) Pagination(c core.IHTTPContext) error {
	input := &requests.TimesheetPaginationRequest{}
	if err := c.Bind(input); err != nil {
		ierr := core.Error{
			Status:  http.StatusBadRequest,
			Code:    "INVALID_PARAMS",
			Message: "Invalid params request",
		}
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	timesheetSvc := services.NewTimesheetService(c)
	res, ierr := timesheetSvc.Pagination(c.GetPageOptions(), &services.TimesheetPaginationOptions{
		UserID:    input.UserID,
		StartDate: input.StartDate,
		EndDate:   input.EndDate,
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m TimesheetAdminController) Find(c core.IHTTPContext) error {
	timesheetSvc := services.NewTimesheetService(c)
	timesheet, err := timesheetSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, timesheet)
}

func (m TimesheetAdminController) SummaryReport(c core.IHTTPContext) error {
	input := &requests.TimesheetSummaryReportRequest{}
	if err := c.Bind(input); err != nil {
		ierr := core.Error{
			Status:  http.StatusBadRequest,
			Code:    "INVALID_PARAMS",
			Message: "Invalid params request",
		}
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	if ierr := input.Validate(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	timesheetSvc := services.NewTimesheetService(c)
	res, ierr := timesheetSvc.SummaryReport(&services.TimesheetSummaryReportOptions{
		StartDate: input.StartDate,
		EndDate:   input.EndDate,
		Team:      input.Team,
	})
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}
